import { Divider } from '@/components/divider/divider';
import Button from '@/components/button/button';
import React, { useEffect } from 'react';
import { useCancelPlan } from '@/hooks/useCancelPlan';
import { Alert } from '@/components/alert/alert';
import { FormAlert } from '@/components/dismissable-alert/dismissable-alert';
import { useCurrentPlanId } from '@/context/current-plan-context';

interface CancelPlanProps {
  setIsOpen: (isOpen: boolean) => void;
}

export function CancelPlan({ setIsOpen }: CancelPlanProps) {
  const currentPlanId = useCurrentPlanId();
  const {
    cancelPlan,
    isPending,
    error: mutationError,
    isSuccess
  } = useCancelPlan();

  // Auto-close modal after showing success message
  useEffect(() => {
    if (isSuccess) {
      const timer = setTimeout(() => {
        setIsOpen(false);
      }, 3000); // Close after 3 seconds

      return () => clearTimeout(timer);
    }
  }, [isSuccess, setIsOpen]);

  const handleCancel = () => setIsOpen(false);

  const handleConfirm = () => {
    if (!currentPlanId) return;
    cancelPlan(currentPlanId);
  };

  return (
    <>
      {isSuccess ? (
        <CancelPlanConfirmation />
      ) : (
        <CancelPlanForm
          onCancel={handleCancel}
          onConfirm={handleConfirm}
          isPending={isPending}
          error={mutationError}
        />
      )}
    </>
  );
}

interface CancelPlanFormProps {
  onCancel: () => void;
  onConfirm: () => void;
  isPending: boolean;
  error: Error | null;
}

function CancelPlanForm({
  onCancel,
  onConfirm,
  isPending,
  error
}: CancelPlanFormProps) {
  return (
    <>
      <CancelPlanWarningText />
      <Divider className="my-4" />
      <CancelPlanActionButtons
        onCancel={onCancel}
        onConfirm={onConfirm}
        isPending={isPending}
      />
      <CancelPlanErrorAlert error={error} />
    </>
  );
}

function CancelPlanWarningText() {
  return (
    <>
      <p>
        Once you confirm your cancellation, this plan will remain active until
        20/09/2025 and then will be cancelled.
      </p>
      <br />
      <p>
        To keep your number for your next provider please text &quot;PAC&quot;
        to 65075.
      </p>
    </>
  );
}

interface CancelPlanActionButtonsProps {
  onCancel: () => void;
  onConfirm: () => void;
  isPending: boolean;
}

function CancelPlanActionButtons({
  onCancel,
  onConfirm,
  isPending
}: CancelPlanActionButtonsProps) {
  return (
    <div className="flex flex-wrap gap-4">
      <Button
        onClick={onCancel}
        variant="secondary"
        className="grow sm:basis-[calc(50%-8px)]"
      >
        I&apos;ve changed my mind
      </Button>
      <Button
        isLoading={isPending}
        disabled={isPending}
        onClick={onConfirm}
        variant="primary"
        className="grow sm:basis-[calc(50%-8px)]"
      >
        Cancel my plan
      </Button>
    </div>
  );
}

interface CancelPlanErrorAlertProps {
  error: Error | null;
}

function CancelPlanErrorAlert({ error }: CancelPlanErrorAlertProps) {
  if (!error?.message) return null;

  return (
    <Alert
      variant="error"
      message={error.message}
      align="left"
      className="my-4"
    />
  );
}

function CancelPlanConfirmation() {
  return (
    <FormAlert
      dismissible={false}
      variant="success"
      title="Your plan has been cancelled"
      messagesStyles="space-y-4"
      messages={[
        `yourPlanRemainsActiveUntil: You can still use your plan until 20/09/2025.`,
        `lastPaymentDate: The last payment for this plan will be taken on 20/09/2025.`,
        `keepYourNumber: To keep your number for your next provider please text "PAC" to 65075.`
      ]}
    />
  );
}
