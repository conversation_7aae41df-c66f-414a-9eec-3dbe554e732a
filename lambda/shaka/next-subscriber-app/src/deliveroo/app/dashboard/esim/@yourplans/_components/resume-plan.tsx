import { Divider } from '@/components/divider/divider';
import Button from '@/components/button/button';
import React, { useEffect } from 'react';
import { Alert } from '@/components/alert/alert';
import { FormAlert } from '@/components/dismissable-alert/dismissable-alert';
import useGetCurrentlySelectedPlan from '@/hooks/useGetCurrentlySelectedPlan';
import { useResumePlan } from '@/hooks/useResumePlan';

interface ResumePlanProps {
  setIsOpen: (isOpen: boolean) => void;
}

export function ResumePlan({ setIsOpen }: ResumePlanProps) {
  const currentSubscription = useGetCurrentlySelectedPlan();
  const migrationId = currentSubscription?.current_migration?.id;

  const {
    resumePlan,
    isPending,
    error: mutationError,
    isSuccess
  } = useResumePlan();

  // Auto-close modal after showing success message
  useEffect(() => {
    if (isSuccess) {
      const timer = setTimeout(() => {
        setIsOpen(false);
      }, 3000); // Close after 3 seconds

      return () => clearTimeout(timer);
    }
  }, [isSuccess, setIsOpen]);

  const handleConfirm = () => {
    if (!migrationId) return;
    resumePlan(migrationId);
  };

  return (
    <>
      {isSuccess ? (
        <ResumePlanConfirmation />
      ) : (
        <ResumePlanForm
          onCancel={() => setIsOpen(false)}
          onConfirm={handleConfirm}
          isPending={isPending}
          error={mutationError}
        />
      )}
    </>
  );
}

interface ResumePlanFormProps {
  onCancel: () => void;
  onConfirm: () => void;
  isPending: boolean;
  error: Error | null;
}

function ResumePlanForm({
  onCancel,
  onConfirm,
  isPending,
  error
}: ResumePlanFormProps) {
  return (
    <>
      <ResumePlanInfoText />
      <Divider className="my-4" />
      <ResumePlanActionButtons
        onCancel={onCancel}
        onConfirm={onConfirm}
        isPending={isPending}
      />
      <ResumePlanErrorAlert error={error} />
    </>
  );
}

function ResumePlanInfoText() {
  return (
    <>
      <p>
        Resuming will cancel your pending cancellation request and your plan
        will continue as normal.
      </p>
      <br />
      <p>Do you want to resume your subscription?</p>
    </>
  );
}

interface ResumePlanActionButtonsProps {
  onCancel: () => void;
  onConfirm: () => void;
  isPending: boolean;
}

function ResumePlanActionButtons({
  onCancel,
  onConfirm,
  isPending
}: ResumePlanActionButtonsProps) {
  return (
    <div className="flex flex-wrap gap-4">
      <Button
        onClick={onCancel}
        variant="secondary"
        className="grow hover:border-black hover:text-black! sm:basis-[calc(50%-8px)]"
      >
        No, keep cancellation
      </Button>
      <Button
        isLoading={isPending}
        disabled={isPending}
        onClick={onConfirm}
        variant="primary"
        className="grow sm:basis-[calc(50%-8px)]"
      >
        Resume subscription
      </Button>
    </div>
  );
}

interface ResumePlanErrorAlertProps {
  error: Error | null;
}

function ResumePlanErrorAlert({ error }: ResumePlanErrorAlertProps) {
  if (!error?.message) return null;

  return (
    <Alert
      variant="error"
      message={error.message}
      align="left"
      className="my-4"
    />
  );
}

function ResumePlanConfirmation() {
  return (
    <FormAlert
      dismissible={false}
      variant="deliveroo"
      title="Your subscription has been resumed"
      messagesStyles="space-y-4"
      messages={[
        `resumeSuccess: Your plan will continue and scheduled cancellation has been removed.`
      ]}
    />
  );
}
